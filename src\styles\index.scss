@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

a:hover {
  color: #409eff;
}
div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

.mo-link {
  color: #409eff;
}

.flex {
  display: flex;
}
.items-center {
  align-items: center;
}

.ml-sm {
  margin-left: 10px;
}
.ml-md {
  margin-left: 20px;
}
.mt-sm {
  margin-top: 10px;
}
.mt-md {
  margin-top: 20px;
}
.color--danger,
.color--danger:hover {
  color: #f56c6c;
}

.font-medium {
  font-weight: 500;
}
