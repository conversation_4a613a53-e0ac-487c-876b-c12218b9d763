// sidebar
$menuText:#bfcbd9;
$menuActiveText:#409EFF;
$subMenuActiveText:#f4f4f5; //https://github.com/ElemeFE/element/issues/12951

$menuBg:#304156;
$menuHover:#263445;

$subMenuBg:#1f2d3d;
$subMenuHover:#001528;

// $sideBarWidth: 210px;
$sideBarWidth: 150px;

// 菜单item字体大小和高度
$sideBarMenuItemFontSize: 14px;
$sideBarMenuItemHeight: 49px;
$sideBarMenuItemLineHeight: 49px;

     
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
  sideBarMenuItemFontSize: $sideBarMenuItemFontSize;
  sideBarMenuItemHeight: $sideBarMenuItemHeight;
  sideBarMenuItemLineHeight: $sideBarMenuItemLineHeight
}
