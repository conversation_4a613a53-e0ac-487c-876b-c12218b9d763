{"name": "vue-admin-template", "version": "4.2.1", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "private": true, "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "dep-pro": "npm run build:prod && npm run dep-pub", "dep-pub": "bash publish.sh", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@ckeditor/ckeditor5-build-decoupled-document": "^23.1.0", "@ckeditor/ckeditor5-vue": "^1.0.3", "axios": "0.18.1", "babel-preset-es2015": "^6.24.1", "countup": "^1.8.2", "echarts": "4.0.4", "element-ui": "^2.15.0", "js-cookie": "2.2.0", "less": "^4.2.0", "moment": "^2.29.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qs": "6.5.3", "vue": "2.6.10", "vue-router": "3.0.6", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/babel-preset-app": "3.12.1", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "babel-loader": "8.3.0", "cache-loader": "2.0.1", "chalk": "2.4.2", "connect": "3.6.6", "core-js": "2.6.12", "css-loader": "1.0.1", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "less": "^4.1.3", "less-loader": "^7.2.1", "mockjs": "1.0.1-beta3", "runjs": "^4.3.2", "sass": "~1.32.6", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-baker-runtime": "1.4.7", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "prettier": "^2.7.1", "thread-loader": "2.1.3", "url-loader": "1.1.2", "vue-loader": "15.10.2", "vue-style-loader": "4.1.3", "vue-template-compiler": "2.6.10", "webpack-bundle-analyzer": "^4.5.0", "regenerator-runtime": "0.14.0", "postcss-loader": "3.0.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}